# -----------------------------------------------------------------------------
# Application BASE URL
# https://mksaas.com/docs/env#core-configuration
# For production, set to your domain, e.g. https://mksaas.com
# For development, set to http://localhost:3000 or any other port
# -----------------------------------------------------------------------------
NEXT_PUBLIC_BASE_URL="http://localhost:3000"

# -----------------------------------------------------------------------------
# Database
# https://mksaas.com/docs/database#setup
# -----------------------------------------------------------------------------
DATABASE_URL=""

# -----------------------------------------------------------------------------
# Better Auth
# https://mksaas.com/docs/auth#setup
# Generate a random string for the secret key using `openssl rand -base64 32`
# -----------------------------------------------------------------------------
BETTER_AUTH_SECRET=""

# -----------------------------------------------------------------------------
# Github OAuth
# https://mksaas.com/docs/auth#2-configure-github-oauth
# https://www.better-auth.com/docs/authentication/github
# Get Client information from https://github.com/settings/developers
# -----------------------------------------------------------------------------
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

# -----------------------------------------------------------------------------
# Google OAuth
# https://mksaas.com/docs/auth#3-configure-google-oauth
# https://www.better-auth.com/docs/authentication/google
# Get Client information from https://console.cloud.google.com/apis/credentials
# -----------------------------------------------------------------------------
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# -----------------------------------------------------------------------------
# Email / Newsletter (Resend)
# https://mksaas.com/docs/email#setup
# https://mksaas.com/docs/newsletter#setup
# Get API key and audience id from https://resend.com
# -----------------------------------------------------------------------------
RESEND_API_KEY=""
RESEND_AUDIENCE_ID=""

# -----------------------------------------------------------------------------
# Storage (Cloudflare R2 or S3-compatible service of your choice)
# https://mksaas.com/docs/storage#setup
# Cloudflare R2: https://www.cloudflare.com/developer-platform/products/r2
# -----------------------------------------------------------------------------
STORAGE_REGION="auto"
STORAGE_BUCKET_NAME=""
STORAGE_ACCESS_KEY_ID=""
STORAGE_SECRET_ACCESS_KEY=""
STORAGE_ENDPOINT=""
STORAGE_FORCE_PATH_STYLE="false"
STORAGE_PUBLIC_URL=""

# -----------------------------------------------------------------------------
# Payment (Stripe)
# https://mksaas.com/docs/payment#setup
# Get Stripe key and secret from https://dashboard.stripe.com
# -----------------------------------------------------------------------------
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# Pro plan - monthly subscription
NEXT_PUBLIC_STRIPE_PRICE_PRO_MONTHLY=""
# Pro plan - yearly subscription
NEXT_PUBLIC_STRIPE_PRICE_PRO_YEARLY=""
# Lifetime plan - one-time payment
NEXT_PUBLIC_STRIPE_PRICE_LIFETIME=""

# -----------------------------------------------------------------------------
# Payment (Creem.io)
# https://docs.creem.io/introduction
# Get Creem.io key and product settings yours from https://www.creem.io/dashboard/home/<USER>
# -----------------------------------------------------------------------------
CREEM_API_KEY=
CREEM_API_URL=https://api.creem.io # test api url is https://test-api.creem.io
CREEM_WEBHOOK_SECRET=
CREEM_SUCCESS_URL=
# Creem Product IDs for different plans
NEXT_PUBLIC_CREEM_PRODUCT_ID_PRO_MONTHLY=
NEXT_PUBLIC_CREEM_PRODUCT_ID_PRO_YEARLY=
NEXT_PUBLIC_CREEM_PRODUCT_ID_LIFETIME=

# -----------------------------------------------------------------------------
# Configurations
# -----------------------------------------------------------------------------
# Disable image optimization, check out next.config.ts for more details
DISABLE_IMAGE_OPTIMIZATION=false
# Run this website as demo website, in most cases, you should set this to false
NEXT_PUBLIC_DEMO_WEBSITE=false

# -----------------------------------------------------------------------------
# Analytics
# https://mksaas.com/docs/analytics#setup
# -----------------------------------------------------------------------------
# Google Analytics (https://analytics.google.com)
# https://mksaas.com/docs/analytics#google
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""
# -----------------------------------------------------------------------------
# Umami Analytics (https://umami.is)
# https://mksaas.com/docs/analytics#umami
# -----------------------------------------------------------------------------
NEXT_PUBLIC_UMAMI_WEBSITE_ID=""
NEXT_PUBLIC_UMAMI_SCRIPT="https://cloud.umami.is/script.js"
# -----------------------------------------------------------------------------
# OpenPanel Analytics (https://openpanel.dev)
# https://mksaas.com/docs/analytics#openpanel
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID=""
# -----------------------------------------------------------------------------
# Plausible Analytics (https://plausible.io)
# https://mksaas.com/docs/analytics#plausible
# -----------------------------------------------------------------------------
NEXT_PUBLIC_PLAUSIBLE_DOMAIN=""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT="https://plausible.io/js/script.js"
# -----------------------------------------------------------------------------
# Ahrefs Analytics (https://ahrefs.com)
# https://mksaas.com/docs/analytics#ahrefs
# -----------------------------------------------------------------------------
NEXT_PUBLIC_AHREFS_WEBSITE_ID=""
# -----------------------------------------------------------------------------
# Seline Analytics
# https://mksaas.com/docs/analytics#seline
# -----------------------------------------------------------------------------
NEXT_PUBLIC_SELINE_TOKEN=""
# -----------------------------------------------------------------------------
# DataFast Analytics (https://datafa.st)
# https://mksaas.com/docs/analytics#datafast
# -----------------------------------------------------------------------------
NEXT_PUBLIC_DATAFAST_ANALYTICS_ID=""
NEXT_PUBLIC_DATAFAST_ANALYTICS_DOMAIN=""


# -----------------------------------------------------------------------------
# Discord
# -----------------------------------------------------------------------------
NEXT_PUBLIC_DISCORD_WIDGET_SERVER_ID=""
NEXT_PUBLIC_DISCORD_WIDGET_CHANNEL_ID=""

# -----------------------------------------------------------------------------
# Affiliate
# https://mksaas.com/docs/affiliate
# -----------------------------------------------------------------------------
# Affonso
# https://affonso.com/
# -----------------------------------------------------------------------------
NEXT_PUBLIC_AFFILIATE_AFFONSO_ID=""
# -----------------------------------------------------------------------------
# PromoteKit
# https://www.promotekit.com/
# -----------------------------------------------------------------------------
NEXT_PUBLIC_AFFILIATE_PROMOTEKIT_ID=""

# -----------------------------------------------------------------------------
# Speechify API for Voice Cloning
# https://docs.sws.speechify.com/v1/docs/features/voice-cloning
# Get your API token from https://speechify.com/
# -----------------------------------------------------------------------------
SPEECHIFY_API_TOKEN=""
