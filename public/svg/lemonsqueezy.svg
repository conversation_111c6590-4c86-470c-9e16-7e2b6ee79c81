<svg width="122" height="16" viewBox="0 0 122 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_42_4)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.3337 7.91657H27.9357C27.6958 7.00148 27.0387 6.63045 26.2383 6.63045C25.3583 6.63045 24.6531 7.11011 24.3337 7.91657ZM30.049 9.48154H24.2059C24.4458 10.6602 25.4379 11.0945 26.3022 11.0945C27.3908 11.0945 27.8562 10.4432 27.8562 10.4432H29.8729C29.2642 11.993 27.8079 12.845 26.2384 12.845C24.0769 12.845 22.188 11.2485 22.188 8.83148C22.188 6.42857 24.0612 4.94057 26.1744 4.94057C28.2238 4.94057 30.3058 6.31988 30.049 9.48154Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M49.8991 8.89325C49.8991 7.68417 49.1625 6.73897 47.9461 6.73897C46.7287 6.73897 45.9125 7.68417 45.9125 8.89325C45.9125 10.1023 46.7287 11.0476 47.9461 11.0476C49.1625 11.0476 49.8991 10.1023 49.8991 8.89325ZM43.8149 8.90834C43.8149 6.42857 45.8004 4.94057 47.9461 4.94057C50.1076 4.94057 51.9965 6.44365 51.9965 8.87685C51.9965 11.3417 50.0424 12.8448 47.8811 12.8448C45.7039 12.8448 43.8149 11.3417 43.8149 8.90834Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M60.5665 8.56782V12.7679H58.4362V9.03222C58.4362 8.76851 58.6293 6.81594 57.092 6.73914C56.3386 6.6924 54.9944 7.09485 54.9944 9.12565V12.7679H52.8813V5.01757H54.8211L54.8275 6.09348C54.8275 6.09348 55.7021 4.94057 57.3333 4.94057C59.3985 4.94057 60.5665 6.42857 60.5665 8.56782Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M68.3526 6.58377C67.6801 6.58377 67.3921 6.90937 67.3921 7.23502C67.3921 7.76137 68.1126 7.91657 68.5926 8.01C70.0189 8.30394 71.4595 8.72302 71.4595 10.3649C71.4595 11.9615 70.0983 12.845 68.4492 12.845C66.6086 12.845 65.1835 11.7608 65.0869 10.1176H67.0555C67.1041 10.582 67.4246 11.1865 68.4012 11.1865C69.2172 11.1865 69.4103 10.7689 69.4103 10.4432C69.4103 9.86897 68.8492 9.69851 68.3046 9.5748C67.3606 9.37291 65.3269 9.00194 65.3269 7.23502C65.3269 5.71554 66.8326 4.94057 68.3852 4.94057C70.1778 4.94057 71.3629 5.99445 71.4595 7.2968H69.4892C69.4258 7.03308 69.1703 6.58377 68.3526 6.58377Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77.9322 8.89325C77.9322 7.606 77.2111 6.84742 76.1711 6.84742C75.1945 6.84742 74.2168 7.52897 74.2168 8.89325C74.2168 10.2564 75.1945 10.9391 76.1711 10.9391C77.2111 10.9391 77.9322 10.1794 77.9322 8.89325ZM79.9334 5.01757V15.8675H77.9322V12.0081C77.4202 12.5659 76.6991 12.8448 75.8659 12.8448C73.8339 12.8448 72.1362 11.2333 72.1362 8.89325C72.1362 6.55211 73.8339 4.94057 75.8659 4.94057C77.4637 4.94057 78.0619 5.97857 78.0619 5.97857L78.0585 5.01757H79.9334Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M91.3737 7.91657H94.976C94.736 7.00148 94.0789 6.63045 93.2789 6.63045C92.3989 6.63045 91.6932 7.11011 91.3737 7.91657ZM97.0892 9.48154H91.2452C91.4863 10.6602 92.4783 11.0945 93.3423 11.0945C94.4309 11.0945 94.8966 10.4432 94.8966 10.4432H96.9132C96.3046 11.993 94.848 12.845 93.2789 12.845C91.1172 12.845 89.228 11.2485 89.228 8.83148C89.228 6.42857 91.1017 4.94057 93.2149 4.94057C95.264 4.94057 97.3463 6.31988 97.0892 9.48154Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M99.8801 7.91657H103.482C103.242 7.00148 102.585 6.63045 101.785 6.63045C100.905 6.63045 100.2 7.11011 99.8801 7.91657ZM105.596 9.48154H99.7527C99.9921 10.6602 100.984 11.0945 101.849 11.0945C102.937 11.0945 103.402 10.4432 103.402 10.4432H105.42C104.81 11.993 103.354 12.845 101.785 12.845C99.6235 12.845 97.7344 11.2485 97.7344 8.83148C97.7344 6.42857 99.6075 4.94057 101.721 4.94057C103.77 4.94057 105.852 6.31988 105.596 9.48154Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M112.724 11.0325V12.7678H105.92V11.7444L109.779 6.75417H106.08V5.01758H112.564V6.04097L108.706 11.0325H112.724Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M121.053 5.01758V11.8378V11.993C121.053 14.0855 119.948 15.9459 117.211 15.9459C114.649 15.9459 113.256 14.3177 113.256 13.0936H115.257C115.257 13.0936 115.561 14.1322 117.147 14.1322C118.492 14.1322 119.052 13.3875 119.052 12.3034V11.9778C118.699 12.3653 118.028 12.845 116.827 12.845C114.729 12.845 113.417 11.3734 113.417 9.21892L113.4 5.01758H115.481V8.75332C115.481 9.80703 115.867 11.0478 117.195 11.0478C117.883 11.0478 119.02 10.7221 119.02 8.65989V5.01758H121.053Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.6641 10.4413C20.6641 10.9246 20.8545 11.124 21.2078 11.124C21.4567 11.124 21.6184 11.0951 21.8543 11.0243L21.9861 12.6171C21.5453 12.7584 21.1192 12.8442 20.5755 12.8442C19.3279 12.8442 18.564 12.4467 18.564 10.7253V1.91797H20.6641V10.4413Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.9113 8.56783V12.7679H40.7811V9.03223C40.7811 7.96326 40.8934 6.61423 39.4201 6.73915C39.0369 6.76943 37.9796 6.93966 37.9796 9.12566V12.7679H35.8664V9.03223C35.8664 7.96326 35.9785 6.61423 34.5054 6.73915C34.1208 6.76943 33.0649 6.93966 33.0649 9.12566V12.7679H30.9517V5.01758H32.8917L32.8934 6.09349C32.8934 6.09349 33.6348 4.94057 35.0177 4.94057C36.684 4.94057 37.3381 6.19566 37.3381 6.19566C37.3381 6.19566 38.0555 4.92551 39.8855 4.92551C41.9662 4.92551 42.9113 6.41349 42.9113 8.56783Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M80.8315 9.21772V5.01758H82.9618V8.75332C82.9618 9.01697 82.7687 10.9695 84.3058 11.0464C85.0595 11.0931 86.4035 10.6906 86.4035 8.65989V5.01758H88.5167V12.7679H86.5795L86.5704 11.6921C86.5704 11.6921 85.6955 12.845 84.0647 12.845C81.9995 12.845 80.8315 11.357 80.8315 9.21772Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.95953 9.82034L8.25169 11.8047C8.78369 12.0508 9.15918 12.4638 9.36198 12.9375C9.87489 14.1371 9.17387 15.3639 8.07341 15.8052C6.97272 16.2462 5.79969 15.9624 5.26631 14.7149L3.39836 10.3352C3.25361 9.99571 3.61724 9.66211 3.95953 9.82034Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.2166 8.53577L8.64726 6.86091C10.1198 6.30428 11.7283 7.35748 11.7066 8.88777C11.7062 8.90777 11.7059 8.92771 11.7054 8.94788C11.6735 10.4381 10.1098 11.4397 8.6696 10.9125L4.2208 9.28417C3.86592 9.15434 3.8633 8.66931 4.2166 8.53577Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.96857 7.95566L8.32406 6.10497C9.77137 5.48992 10.1387 3.64397 9.00514 2.57739C8.99029 2.56334 8.97543 2.54946 8.9604 2.53559C7.84903 1.50404 6.01189 1.86724 5.37919 3.22594L3.4247 7.42371C3.26876 7.75846 3.6212 8.1032 3.96857 7.95566Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.84771 7.22434L4.43123 2.88237C4.62755 2.344 4.59119 1.79497 4.38822 1.32126C3.87425 0.12216 2.48234 -0.264902 1.38202 0.176995C0.281877 0.619063 -0.339783 1.62302 0.194641 2.87002L2.07483 7.24497C2.22063 7.584 2.72149 7.57063 2.84771 7.22434Z" fill="#1E1E1E" style="fill:#1E1E1E;fill:color(display-p3 0.1176 0.1176 0.1176);fill-opacity:1;"/>
</g>
<defs>
<clipPath id="clip0_42_4">
<rect width="121.143" height="16" fill="white" style="fill:white;fill-opacity:1;"/>
</clipPath>
</defs>
</svg>
