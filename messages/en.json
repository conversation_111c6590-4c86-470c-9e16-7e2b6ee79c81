{"Metadata": {"name": "FlowChart AI", "title": "FlowChart AI – Smart AI Tool to Create Beautiful Flowcharts", "description": "Design professional flowcharts faster with FlowChart AI. Draw, edit, and collaborate in real time using powerful AI tools—no design experience required."}, "Common": {"login": "Log in", "logout": "Log out", "signUp": "Sign up", "language": "Switch language", "mode": {"label": "Toggle mode", "light": "Light", "dark": "Dark", "system": "System"}, "theme": {"label": "Toggle theme", "default": "<PERSON><PERSON><PERSON>", "blue": "Blue", "green": "Green", "amber": "Amber", "neutral": "Neutral"}, "copy": "Copy", "saving": "Saving...", "save": "Save", "loading": "Loading...", "cancel": "Cancel", "logoutFailed": "Failed to log out"}, "PricingPage": {"title": "Pricing", "description": "Choose the plan that works best for you", "subtitle": "Choose the plan that works best for you", "monthly": "Monthly", "yearly": "Yearly", "PricingCard": {"freePrice": "$0", "perMonth": "/month", "perYear": "/year", "popular": "Popular", "currentPlan": "Current Plan", "yourCurrentPlan": "Your Current Plan", "getStartedForFree": "Get Started For Free", "getLifetimeAccess": "Get Lifetime Access", "getStarted": "Get Started", "notAvailable": "Not Available", "daysTrial": "{days}-day free trial"}, "CheckoutButton": {"loading": "Loading...", "checkoutFailed": "Failed to open checkout page"}}, "PricePlans": {"free": {"name": "Free", "description": "Start exploring AI-powered flowchart generation — for free, forever.", "features": {"feature-1": "1 AI request per day", "feature-2": "AI-assisted generation and editing", "feature-3": "Unlimited diagram storage", "feature-4": "Text and image input supported", "feature-5": "Community support"}, "limits": {"limit-1": "1 AI request per day", "limit-2": "No technical support", "limit-3": "Standard processing speed"}}, "hobby": {"name": "<PERSON>bby", "description": "Great for individuals who need more AI requests and light technical assistance.", "features": {"feature-1": "500 AI requests per month", "feature-2": "Full access to AI agent for generation and editing", "feature-3": "Unlimited diagram storage", "feature-4": "Text and image input supported", "feature-5": "Email support (24–48h response, max 7 days)", "feature-6": "All generated content belongs to the user"}, "limits": {"limit-1": "No priority support", "limit-2": "Standard processing speed"}}, "professional": {"name": "Professional", "description": "Perfect for power users and professionals who want faster response and priority support.", "features": {"feature-1": "1000 AI requests per month", "feature-2": "Priority AI processing", "feature-3": "Unlimited diagram storage", "feature-4": "Real-time AI-assisted editing", "feature-5": "Email & technical support (under 24h, max 48h)", "feature-6": "Text and image input supported", "feature-7": "All generated content belongs to the user"}, "limits": {"limit-1": "For advanced or customized service needs, please contact our team directly", "limit-2": "Does not include API access or enterprise features"}}, "pro": {"name": "Pro", "description": "Advanced features for professional users and teams.", "features": {"feature-1": "Unlimited AI requests", "feature-2": "Priority processing and support", "feature-3": "Advanced collaboration tools", "feature-4": "Custom integrations", "feature-5": "Enterprise-grade security"}, "limits": {"limit-1": "Advanced features require technical setup", "limit-2": "Enterprise support available separately"}}, "lifetime": {"name": "Lifetime", "description": "One-time payment for lifetime access to all premium features.", "features": {"feature-1": "Lifetime access to all features", "feature-2": "Unlimited AI requests forever", "feature-3": "Priority support for life", "feature-4": "All future updates included", "feature-5": "Advanced collaboration tools", "feature-6": "Custom integrations", "feature-7": "Enterprise-grade security"}}}, "NotFoundPage": {"title": "404", "message": "Sorry, the page you are looking for does not exist.", "backToHome": "Back to home"}, "ErrorPage": {"title": "Oops! Something went wrong!", "tryAgain": "Try again", "backToHome": "Back to home"}, "AboutPage": {"title": "About", "description": "This is MkSaaS, an AI SaaS template built with modern technologies, helping you build your SaaS faster and better.", "authorName": "MkSaaS", "authorBio": "AI SaaS Boilerplate", "introduction": "👋 Hi there! This is MkSaaS, an AI SaaS template built with modern technologies, helping you build your SaaS faster and better. If you have any questions, welcome to contact me.", "talkWithMe": "Talk with me", "followMe": "Follow me on X"}, "ChangelogPage": {"title": "Changelog", "description": "Stay up to date with the latest changes in our product", "subtitle": "Stay up to date with the latest changes in our product"}, "ContactPage": {"title": "Contact", "description": "We'll help you find the right plan for your business", "subtitle": "We'll help you find the right plan for your business", "form": {"title": "Contact Us", "description": "If you have any questions or feedback, please reach out to our team", "name": "Name", "email": "Email", "message": "Message", "submit": "Submit", "submitting": "Submitting...", "success": "Message sent successfully", "fail": "Failed to send message", "nameMinLength": "Name must be at least 3 characters", "nameMaxLength": "Name must not exceed 30 characters", "emailValidation": "Please enter a valid email address", "messageMinLength": "Message must be at least 10 characters", "messageMaxLength": "Message must not exceed 500 characters"}}, "Newsletter": {"title": "Newsletter", "subtitle": "Join the community", "description": "Subscribe to our newsletter for the latest news and updates", "form": {"email": "Email", "subscribe": "Subscribe", "subscribing": "Subscribing...", "success": "Subscribed successfully", "fail": "Failed to subscribe", "emailValidation": "Please enter a valid email address"}}, "AuthPage": {"login": {"title": "<PERSON><PERSON>", "welcomeBack": "Welcome back", "email": "Email", "password": "Password", "signIn": "Sign In", "signUpHint": "Don't have an account? Sign up", "forgotPassword": "Forgot Password?", "signInWithGoogle": "Sign In with Google", "signInWithGitHub": "Sign In with GitHub", "showPassword": "Show password", "hidePassword": "Hide password", "or": "Or continue with", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password"}, "register": {"title": "Register", "createAccount": "Create an account", "name": "Name", "email": "Email", "password": "Password", "signUp": "Sign Up", "signInHint": "Already have an account? Sign in", "checkEmail": "Please check your email inbox", "showPassword": "Show password", "hidePassword": "Hide password", "nameRequired": "Please enter your name", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password"}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "send": "Send reset link", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox", "emailRequired": "Please enter your email"}, "resetPassword": {"title": "Reset Password", "password": "Password", "reset": "Reset password", "backToLogin": "Back to login", "showPassword": "Show password", "hidePassword": "Hide password", "minLength": "Password must be at least 8 characters"}, "error": {"title": "Oops! Something went wrong!", "tryAgain": "Please try again.", "backToLogin": "Back to login", "checkEmail": "Please check your email inbox"}, "common": {"termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "byClickingContinue": "By clicking continue, you agree to our ", "and": " and "}}, "BlogPage": {"title": "Blog", "description": "Latest news and updates from our team", "subtitle": "Latest news and updates from our team", "author": "Author", "categories": "Categories", "tableOfContents": "Table of Contents", "readTime": "{minutes} min read", "all": "All", "noPostsFound": "No posts found", "allPosts": "All Posts", "morePosts": "More Posts"}, "DocsPage": {"toc": "Table of Contents", "search": "Search docs", "lastUpdate": "Last updated on", "searchNoResult": "No results", "previousPage": "Previous", "nextPage": "Next", "chooseLanguage": "Select language", "title": "MkSaaS Docs", "homepage": "Homepage", "blog": "Blog"}, "Marketing": {"navbar": {"features": {"title": "Features"}, "pricing": {"title": "Pricing"}, "blog": {"title": "Blog"}, "docs": {"title": "Docs"}, "pages": {"title": "Pages", "items": {"about": {"title": "About", "description": "Learn more about our company and mission"}, "contact": {"title": "Contact", "description": "Get in touch with our team"}, "changelog": {"title": "Changelog", "description": "See what's new in our latest updates"}, "cookiePolicy": {"title": "<PERSON><PERSON>", "description": "Learn about our cookie usage and privacy"}, "privacyPolicy": {"title": "Privacy Policy", "description": "Read our privacy policy and data protection"}, "termsOfService": {"title": "Terms of Service", "description": "Review our terms and conditions"}}}, "ai": {"title": "AI Tools", "items": {"text": {"title": "AI Text", "description": "Show how to use AI to write stunning text"}, "image": {"title": "AI Image", "description": "Show how to use AI to generate beautiful images"}, "video": {"title": "AI Video", "description": "Show how to use AI to generate amazing videos"}, "audio": {"title": "AI Audio", "description": "Show how to use AI to generate wonderful audio"}}}}, "footer": {"tagline": "Create beautiful flowcharts with FlowChart AI - fast, intelligent, and effortless", "product": {"title": "Product", "items": {"features": "Features", "pricing": "Pricing", "faq": "FAQ"}}, "resources": {"title": "Resources", "items": {"blog": "Blog", "docs": "Documentation", "changelog": "Changelog"}}, "company": {"title": "Company", "items": {"about": "About", "contact": "Contact"}}, "legal": {"title": "Legal", "items": {"cookiePolicy": "<PERSON><PERSON>", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}}}, "avatar": {"dashboard": "Dashboard", "billing": "Billing", "settings": "Settings"}}, "Dashboard": {"dashboard": {"title": "Dashboard"}, "admin": {"title": "Admin", "users": {"title": "Users", "fakeData": "Note: Faked data for demonstration, some features are disabled", "error": "Failed to get users", "search": "Search users...", "columns": {"columns": "Columns", "name": "Name", "email": "Email", "role": "Role", "createdAt": "Created At", "customerId": "Customer ID", "status": "Status", "banReason": "Ban Reason", "banExpires": "Ban Expires"}, "noResults": "No results", "firstPage": "First Page", "lastPage": "Last Page", "nextPage": "Next Page", "previousPage": "Previous Page", "rowsPerPage": "Rows per page", "page": "Page", "loading": "Loading...", "admin": "Admin", "user": "User", "email": {"verified": "<PERSON><PERSON>", "unverified": "Email Unverified"}, "emailCopied": "Email copied to clipboard", "banned": "Banned", "active": "Active", "joined": "Joined", "updated": "Updated", "ban": {"reason": "Ban Reason", "reasonPlaceholder": "Enter the reason for banning this user", "defaultReason": "Spamming", "never": "Never", "expires": "Ban Expires", "selectDate": "Select Date", "button": "Ban User", "success": "User has been banned", "error": "Failed to ban user"}, "unban": {"button": "Unban User", "success": "User has been unbanned", "error": "Failed to unban user"}, "close": "Close"}}, "settings": {"title": "Settings", "profile": {"title": "Profile", "description": "Manage your account information", "avatar": {"title": "Avatar", "description": "Click upload button to upload a custom one", "recommendation": "An avatar is optional but strongly recommended", "uploading": "Uploading...", "uploadAvatar": "Upload Avatar", "success": "Avatar updated successfully", "fail": "Failed to update avatar"}, "name": {"title": "Name", "description": "Please enter your display name", "placeholder": "Enter your name", "minLength": "Please use 3 characters at minimum", "maxLength": "Please use 30 characters at maximum", "hint": "Please use 3-30 characters for your name", "success": "Name updated successfully", "fail": "Failed to update name", "saving": "Saving...", "save": "Save"}}, "billing": {"title": "Billing", "description": "Manage your subscription and billing details", "status": {"active": "Active", "trial": "Trial", "canceled": "Canceled", "free": "Free", "lifetime": "Lifetime"}, "interval": {"month": "month", "year": "year", "oneTime": "one-time"}, "currentPlan": {"title": "Current Plan", "description": "Your current plan details", "noPlan": "You have no active plan"}, "CustomerPortalButton": {"loading": "Loading...", "createCustomerPortalFailed": "Failed to open Stripe customer portal"}, "price": "Price:", "nextBillingDate": "Next billing date:", "trialEnds": "Trial ends:", "serviceEnds": "Service ends:", "freePlanMessage": "You are currently on the free plan with limited features", "lifetimeMessage": "You have lifetime access to all premium features", "manageSubscription": "Manage Subscription and Billing", "manageBilling": "Manage Billing", "upgradePlan": "Upgrade Plan", "retry": "Retry", "errorMessage": "Failed to get data"}, "notification": {"title": "Notification", "description": "Manage your notification preferences", "newsletter": {"title": "Newsletter Subscription", "description": "Manage your newsletter subscription preferences", "label": "Subscribe to newsletter", "hint": "You can change your subscription preferences at any time", "emailRequired": "Email is required to subscribe to the newsletter", "subscribeSuccess": "Successfully subscribed to the newsletter", "subscribeFail": "Failed to subscribe to the newsletter", "unsubscribeSuccess": "Successfully unsubscribed from the newsletter", "unsubscribeFail": "Failed to unsubscribe from the newsletter", "error": "An error occurred while updating your subscription"}}, "security": {"title": "Security", "description": "Manage your security settings", "updatePassword": {"title": "Change Password", "description": "Enter your current password and a new password", "currentPassword": "Current Password", "currentRequired": "Current password is required", "newPassword": "New Password", "newMinLength": "Password must be at least 8 characters", "hint": "Please use at least 8 characters for password", "showPassword": "Show password", "hidePassword": "Hide password", "success": "Password updated successfully", "fail": "Failed to update password", "saving": "Saving...", "save": "Save"}, "resetPassword": {"title": "Reset Password", "description": "Reset password to enable email login", "info": "Resetting your password will allow you to sign in using your email and password in addition to your social login methods. You will receive an email with instructions to reset your password", "button": "Reset Password"}, "deleteAccount": {"title": "Delete Account", "description": "Permanently remove your account and all of its contents", "warning": "This action is not reversible, so please continue with caution", "button": "Delete Account", "confirmTitle": "Delete Account", "confirmDescription": "Are you sure you want to delete your account? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "success": "Account deleted successfully", "fail": "Failed to delete account"}}}, "upgrade": {"title": "Upgrade", "description": "Upgrade to Pro to access all features", "button": "Upgrade"}}, "Mail": {"common": {"team": "{name} Team", "copyright": "©️ {year} All Rights Reserved."}, "verifyEmail": {"title": "Hi, {name}.", "body": "Please click the link below to verify your email address.", "confirmEmail": "Confirm email", "subject": "Verify your email"}, "forgotPassword": {"title": "Hi, {name}.", "body": "Please click the link below to reset your password.", "resetPassword": "Reset password", "subject": "Reset your password"}, "subscribeNewsletter": {"body": "Thank you for subscribing to the newsletter. We will keep you updated with the latest news and updates.", "subject": "Thanks for subscribing"}, "contactMessage": {"name": "Name: {name}", "email": "Email: {email}", "message": "Message: {message}", "subject": "Contact Message from Website"}}, "HomePage": {"title": "MkSaaS", "description": "Make AI SaaS in days, simply and effortlessly", "hero": {"title": "Create Beautiful Flowcharts with FlowChart AI", "description": "FlowChart AI helps you transform ideas into detailed, professional flowcharts instantly. Use natural language to design, edit, and collaborate on diagrams—no design skills required.", "introduction": "Introducing FlowChart AI", "primary": "Start Creating", "secondary": "See <PERSON><PERSON>"}, "logocloud": {"title": "Your favorite companies are our partners"}, "useCases": {"title": "Use Cases", "subtitle": "Let AI Handle the Heavy Lifting", "description": "FlowChart AI helps you map out complex processes, brainstorm ideas, and build professional diagrams—faster and easier than ever before.", "tryNow": "Start with FlowChart AI", "items": {"item-1": {"title": "Product Management", "description": "Transform product ideas into actionable roadmaps. Create detailed user journey maps, feature prioritization matrices, and sprint planning workflows. AI helps you visualize complex product ecosystems and stakeholder relationships with precision."}, "item-2": {"title": "Software Development", "description": "Architect robust systems with confidence. Design microservices architectures, API integration flows, database relationships, and deployment pipelines. Perfect for technical documentation and system design reviews."}, "item-3": {"title": "Business Analysis", "description": "Unlock insights through visual process mapping. Create comprehensive business process flows, decision trees, risk assessment matrices, and operational optimization diagrams that drive strategic decisions."}, "item-4": {"title": "Education & Training", "description": "Enhance learning through visual storytelling. Build interactive curriculum maps, concept relationship diagrams, assessment workflows, and training progression paths that engage students and improve comprehension."}, "item-5": {"title": "Startup Planning", "description": "Turn startup vision into executable strategy. Visualize business model canvases, funding pipelines, go-to-market strategies, and operational workflows. Essential for pitch decks and investor presentations."}, "item-6": {"title": "Project Management", "description": "Orchestrate complex projects with clarity. Design project timelines, resource allocation flows, risk mitigation processes, and team collaboration workflows. Keep stakeholders aligned and projects on track."}}}, "demo": {"title": "See FlowChart AI in Action", "subtitle": "From Idea to Flowchart in Seconds", "description": "Watch how FlowChart AI turns natural language prompts into precise, presentation-ready flowcharts — no design skills or manual effort needed.", "tryItNow": "Use FlowChart AI Now", "features": {"feature-1": {"title": "Natural Language Input", "description": "Simply describe your process in Language you like — no shapes, templates, or special tools required"}, "feature-2": {"title": "AI-Powered Generation", "description": "FlowChart AI Automatically generates clean, logical diagrams based on your input"}, "feature-3": {"title": "Instant Editing", "description": "Customize and refine your diagram, Easily adjust elements, add details, and make it your own"}}}, "howItWorks": {"title": "Start in 3 Easy Steps", "subtitle": "No Experience Needed", "description": "FlowChart AI makes creating flowcharts as simple as typing your thoughts. Whether you're a beginner or a pro, you can go from messy ideas to structured diagrams in minutes—without any design tools.", "steps": {"step-1": {"title": "Just Type What You Need", "description": "Start by describing your process, structure, or system in natural language. FlowChart AI understands what you mean—even if it's not perfectly written.", "details": ["Describe your workflow in plain English—FlowChart AI handles the diagram generation", "FlowChart AI supports business logic, software systems, and educational use cases", "AI-powered understanding of both concise and complex process descriptions"]}, "step-2": {"title": "FlowChart AI Builds Your Diagram", "description": "FlowChart AI transforms your input into a clean, structured flowchart using intelligent shape detection and layout optimization.", "details": ["Automatically generates accurate diagrams with FlowChart AI’s layout engine", "Optimized visual structure for readability and presentation", "No need to manually draw—FlowChart AI does it all for you"]}, "step-3": {"title": "Edit, Manage & Save", "description": "Make adjustments using FlowChart AI’s intuitive editor. Save diagrams to your personal workspace, manage your flowcharts, and return to refine or export when needed.", "details": ["Edit using FlowChart AI’s drag-and-drop canvas—fast and intuitive", "Organize all your diagrams in a personal FlowChart AI workspace", "Export in Excalidraw format; PNG, SVG, and PDF exports coming soon"]}}, "getStarted": "Using FlowChart AI Now"}, "aiCapabilities": {"title": "FlowChart AI Capabilities", "subtitle": "Smarter Diagrams Powered by Advanced AI", "description": "FlowChart AI uses state-of-the-art language models and layout engines to turn your ideas into perfect, presentation-ready flowcharts—fast, accurate, and effortless.", "capability-1": "Natural Language Processing – FlowChart AI understands messy or vague input and translates it into structured diagrams", "capability-2": "Smart Layout Engine – Automatically arranges elements logically and visually for maximum clarity", "capability-3": "AI-Powered Edits – Update and expand existing flowcharts without starting over", "capability-4": "Multi-language Input – Describe your flowcharts in any language you’re comfortable with", "capability-5": "Context-Aware Suggestions – FlowChart AI intelligently recommends diagram elements and connections based on your intent"}, "comparison": {"title": "Why Choose FlowChart AI?", "subtitle": "See the Difference", "description": "Compare FlowChart AI with traditional flowchart tools and see why professionals choose us.", "traditional": {"title": "Traditional Tools", "subtitle": "Time-consuming and complex", "item-1": "Hours of manual drawing and positioning", "item-2": "Steep learning curve for complex software", "item-3": "Difficult collaboration and sharing", "item-4": "Manual updates when changes are needed", "item-5": "Limited template and shape libraries", "item-6": "Version control and backup challenges", "item-7": "Expensive licensing and subscription fees", "item-8": "Platform-specific compatibility issues"}, "flowchartAi": {"title": "FlowChart AI", "subtitle": "Fast, intelligent, and intuitive", "item-1": "Generate flowcharts in seconds with AI", "item-2": "Natural language input - no training needed", "item-3": "Built-in collaboration and real-time sharing", "item-4": "AI-assisted modifications and updates", "item-5": "Unlimited templates and smart suggestions", "item-6": "Automatic version history and cloud backup", "item-7": "Affordable pricing with generous free tier", "item-8": "Works seamlessly across all devices and browsers"}}, "integration": {"title": "Integrations", "subtitle": "Integrate with your favorite tools", "description": "Connect seamlessly with popular platforms and services to enhance your workflow.", "learnMore": "Learn More", "items": {"item-1": {"title": "Google Gemini", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-2": {"title": "Replit", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-3": {"title": "MagicUI", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-4": {"title": "VSCodium", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-5": {"title": "MediaWiki", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}, "item-6": {"title": "Google PaLM", "description": "Amet praesentium deserunt ex commodi tempore fuga voluptatem."}}}, "integration2": {"title": "Integrate with your favorite tools", "description": "Connect seamlessly with popular platforms and services to enhance your workflow.", "primaryButton": "Get Started", "secondaryButton": "See <PERSON><PERSON>"}, "features": {"title": "Features", "subtitle": "AI-Powered Flowchart Creation", "description": "FlowChart AI offers a comprehensive set of features designed to make diagramming effortless, intelligent, and collaborative.", "items": {"item-1": {"title": "Natural Language to Diagram", "description": "Describe your process or idea in plain language, and let AI instantly turn it into a professional flowchart. No design skills or technical jargon required—just explain your workflow and see it visualized."}, "item-2": {"title": "Personal Workspace & Diagram Library", "description": "Keep all your diagrams organized in one place. Our platform provides a personal workspace where you can access, manage, and continue editing your saved diagrams with ease."}, "item-3": {"title": "Intuitive Drag-and-Drop Editor", "description": "Easily refine and customize your diagram with a user-friendly canvas. Move elements, adjust connections, and apply styles with simple drag-and-drop—no learning curve."}, "item-4": {"title": "Export & Integrate Anywhere", "description": "Export your diagrams in Excalidraw format for editing or backup. Support for PNG, SVG, and PDF exports is coming soon to broaden your sharing and presentation options."}}}, "features2": {"title": "Features2", "subtitle": "The features of your product", "description": "Write the description of your product here", "feature-1": "Product Feature One", "feature-2": "Product Feature Two", "feature-3": "Product Feature Three", "feature-4": "Product Feature Four"}, "features3": {"title": "Features3", "subtitle": "The features of your product", "description": "Write the description of your product here", "items": {"item-1": {"title": "Product Feature One", "description": "Please write the detailed description of feature one here"}, "item-2": {"title": "Product Feature Two", "description": "Please write the detailed description of feature two here"}, "item-3": {"title": "Product Feature Three", "description": "Please write the detailed description of feature three here"}, "item-4": {"title": "Product Feature Four", "description": "Please write the detailed description of feature four here"}, "item-5": {"title": "Product Feature Five", "description": "Please write the detailed description of feature five here"}, "item-6": {"title": "Product Feature Six", "description": "Please write the detailed description of feature six here"}}}, "pricing": {"title": "Pricing", "subtitle": "Pricing", "description": "Choose the plan that works best for you"}, "faqs": {"title": "FAQ", "subtitle": "Frequently Asked Questions", "items": {"item-1": {"question": "Is there a free version of FlowChart AI?", "answer": "Yes. FlowChart AI is open-sourced under the MIT License and available on GitHub. You can self-host and modify it freely. We also offer a free tier — every registered user can generate one AI flowchart per day for free."}, "item-2": {"question": "What formats can I export my diagrams to?", "answer": "Currently, we support exporting flowcharts in Excalidraw format only. In future updates, we plan to add support for PDF, PNG, and SVG formats."}, "item-3": {"question": "Can I manage my flowcharts and access them later?", "answer": "Yes. FlowChart AI provides a personal workspace where you can save, manage, and edit your flowcharts anytime, from anywhere."}, "item-4": {"question": "What AI capabilities does FlowChart AI offer?", "answer": "FlowChart AI allows users to describe their processes or workflows in natural language. The AI interprets the input, handles intelligent layout and diagram structure, and generates editable flowcharts accordingly."}, "item-5": {"question": "How can I upgrade or change my plan?", "answer": "Yes. You can freely upgrade, downgrade, or cancel your plan through your account's billing settings."}, "item-6": {"question": "How complex of a flowchart can FlowChart AI generate?", "answer": "There is no strict limit on the complexity of flowcharts. The actual capability depends on the content and structure of your input."}, "item-7": {"question": "How do I write effective prompts?", "answer": "Use clear natural language and structured steps. You may optionally upload reference images to aid AI understanding. FlowChart AI will then convert your input into a structured diagram."}, "item-8": {"question": "Can teams collaborate on flowcharts?", "answer": "Currently, FlowChart AI is built for individual use. Team collaboration and shared workspaces are part of our future roadmap."}, "item-9": {"question": "Can I generate diagrams from uploaded documents?", "answer": "At the moment, FlowChart AI supports text and image inputs only. Support for document uploads including PDF, Markdown, and other formats is under active development."}}}, "testimonials": {"title": "Testimonials", "subtitle": "What our customers are saying", "items": {"item-1": {"name": "<PERSON>", "role": "Software Engineer", "image": "https://randomuser.me/api/portraits/men/1.jpg", "quote": "MkSaaS is really extraordinary and very practical, no need to break your head. A real gold mine."}, "item-2": {"name": "<PERSON>", "role": "GDE - Android", "image": "https://randomuser.me/api/portraits/men/6.jpg", "quote": "With no experience in webdesign I just redesigned my entire website in a few minutes with tailwindcss thanks to MkSaaS."}, "item-3": {"name": "<PERSON><PERSON>", "role": "Tailkits Creator", "image": "https://randomuser.me/api/portraits/men/7.jpg", "quote": "Great work on MkSaaS template. This is one of the best personal website that I have seen so far :)"}, "item-4": {"name": "Anonymous author", "role": "Product Manager", "image": "https://randomuser.me/api/portraits/men/8.jpg", "quote": "I downloaded the one of MkSaaS template which is very clear to understand at the start and you could modify the codes/blocks to fit perfectly on your purpose of the page."}, "item-5": {"name": "<PERSON><PERSON><PERSON>", "role": "Senior Software Engineer", "image": "https://randomuser.me/api/portraits/men/4.jpg", "quote": "MkSaaS is redefining the standard of web design, with these blocks it provides an easy and efficient way for those who love beauty but may lack the time to implement it."}, "item-6": {"name": "<PERSON><PERSON> Fred", "role": "Fullstack Developer", "image": "https://randomuser.me/api/portraits/men/2.jpg", "quote": "I absolutely love MkSaaS! The component blocks are beautifully designed and easy to use, which makes creating a great-looking website a breeze."}, "item-7": {"name": "<PERSON><PERSON><PERSON>", "role": "Founder of ChatExtend", "image": "https://randomuser.me/api/portraits/men/5.jpg", "quote": "MkSaaS is the perfect fusion of simplicity and versatility, enabling us to create UIs that are as stunning as they are user-friendly."}, "item-8": {"name": "<PERSON>", "role": "Fullstack Developer", "image": "https://randomuser.me/api/portraits/men/9.jpg", "quote": "MkSaaS has transformed the way I develop web applications. Their extensive collection of UI components, blocks, and templates has significantly accelerated my workflow."}, "item-9": {"name": "<PERSON><PERSON><PERSON>", "role": "MerakiUI Creator", "image": "https://randomuser.me/api/portraits/men/10.jpg", "quote": "MkSaaS is an elegant, clean, and responsive tailwind css components it's very helpful to start fast with your project."}, "item-10": {"name": "<PERSON>", "role": "TailwindAwesome Creator", "image": "https://randomuser.me/api/portraits/men/11.jpg", "quote": "I love MkSaaS ❤️. The component blocks are well-structured, simple to use, and beautifully designed. It makes it really easy to have a good-looking website in no time."}, "item-11": {"name": "<PERSON>", "role": "@GoogleDevExpert for Android", "image": "https://randomuser.me/api/portraits/men/12.jpg", "quote": "MkSaaS templates are the perfect solution for anyone who wants to create a beautiful and functional website without any web design experience."}, "item-12": {"name": "<PERSON>", "role": "Software Engineer", "image": "https://randomuser.me/api/portraits/men/13.jpg", "quote": "MkSaaS is so well designed that even with a very poor knowledge of web design you can do miracles. Let yourself be seduced!"}}}, "stats": {"title": "Stats", "subtitle": "MkSaaS in numbers", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "items": {"item-1": {"title": "Stars on GitHub"}, "item-2": {"title": "Active Users"}, "item-3": {"title": "Powered Apps"}}}, "calltoaction": {"title": "Ready to Create Amazing Flowcharts?", "description": "Join thousands of users who are already creating professional diagrams with FlowChart AI", "primaryButton": "Start Creating", "secondaryButton": "Learn More"}}, "AITextPage": {"title": "AI Text", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "AIImagePage": {"title": "AI Image", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "AIVideoPage": {"title": "AI Video", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "AIAudioPage": {"title": "AI Audio", "description": "MkSaaS lets you make AI SaaS in days, simply and effortlessly", "content": "Working in progress"}, "CanvasPage": {"title": "<PERSON><PERSON>", "description": "Create beautiful flowcharts with AI assistance - draw, edit, and collaborate on your diagrams", "subtitle": "AI-Powered Flowchart Editor"}}