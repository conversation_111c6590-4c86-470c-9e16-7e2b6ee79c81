---
description: Best practices for using Radix UI components
globs: *.tsx,*.ts
alwaysApply: false
---

- Use Radix UI primitives for building custom, accessible components
- Utilize Radix UI's composition patterns for creating complex, reusable UI elements
- Implement proper ARIA attributes and keyboard navigation for accessibility
- Utilize Radix UI's built-in state management for complex components
- Customize Radix UI components using the `asChild` prop for better flexibility
