---
description: Best practices for Next.js applications and routing
globs: *.tsx,*.ts
alwaysApply: false
---

- Utilize Next.js 15's new features like Server Actions for improved performance and security
- Implement proper error handling with `error.tsx` and `not-found.tsx` for better user experience
- Use `next-safe-action` for secure form submissions and API calls
- Use the `use client` directive for client components to optimize server-side rendering
- Leverage `next-themes` for easy theme management and dark mode support
