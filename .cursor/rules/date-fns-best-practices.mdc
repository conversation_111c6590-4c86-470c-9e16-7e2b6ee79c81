---
description: Best practices for date and time manipulation with date-fns
globs: *.ts,*.tsx
alwaysApply: false
---

- Use the `format` function for consistent date formatting across your application.
- Implement proper timezone handling using the `utcToZonedTime` function.
- Utilize the `intervalToDuration` function for calculating time differences.
- Leverage the `isWithinInterval` function for date range checks.
