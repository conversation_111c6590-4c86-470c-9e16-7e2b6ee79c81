---
description: Best practices for integrating Stripe payments
globs: *.tsx,*.ts
alwaysApply: false
---

- Use the `@stripe/stripe-js` package for client-side Stripe integration.
- Implement proper error handling and user feedback for payment flows.
- Utilize Stripe's Elements for secure, customizable payment inputs.
- Leverage Stripe's Webhooks for real-time payment status updates.
- Follow Stripe's best practices for PCI compliance and security
