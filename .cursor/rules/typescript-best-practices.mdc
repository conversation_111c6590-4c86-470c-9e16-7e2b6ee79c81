---
description: TypeScript coding standards and type safety guidelines
globs: *.tsx,*.ts
alwaysApply: false
---

- Enable strict mode in your `tsconfig.json` for better type checking.
- Use interfaces for object shapes and types for unions or intersections.
- Leverage type inference where possible to reduce type annotations.
- Use generics for reusable components and functions.
- Use strict null checks to prevent null and undefined errors
- Implement proper type inference using generics for reusable components.
- Utilize type guards and assertions for runtime type checking.
- Use `pnpm` as default package manager if run Command in Terminal.
