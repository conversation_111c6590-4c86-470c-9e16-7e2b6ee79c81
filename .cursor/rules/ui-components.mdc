---
description:
globs: **/*.{ts,tsx}
alwaysApply: false
---
# UI and Components Guide

## Component Structure
- Components in `src/components/`
- Follow atomic design principles
- Use Radix UI primitives
- Implement proper accessibility
- Use Tailwind CSS for styling
- Follow consistent naming
- Keep components focused
- Implement proper error states
- Handle loading states
- Use proper TypeScript types

## UI Libraries
- Radix UI for primitives
- Tai<PERSON>wind CSS for styling
- Framer Motion for animations
- React Hook Form for forms
- Z<PERSON> for validation
- Lucide React for icons
- Tabler Icons for additional icons
- Son<PERSON> for toasts
- <PERSON><PERSON> for drawers
- Embla Carousel for carousels

## Styling Guidelines
- Use Tailwind CSS classes
- Follow design system tokens
- Implement dark mode support
- Use proper spacing scale
- Follow color palette
- Implement responsive design
- Use proper typography
- Handle hover/focus states
- Implement proper transitions
- Use proper z-index scale

## Accessibility
- Use semantic HTML
- Implement proper ARIA labels
- Handle keyboard navigation
- Support screen readers
- Use proper color contrast
- Implement focus management
- Handle dynamic content
- Support reduced motion
- Test with assistive tools
- Follow WCAG guidelines
