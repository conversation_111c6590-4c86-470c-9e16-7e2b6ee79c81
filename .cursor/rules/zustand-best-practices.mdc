---
description: Best practices for state management with Zustand
globs: *.tsx,*.ts
alwaysApply: false
---

- Use the `create` function to define your store for simplicity and performance.
- Implement middleware like `persist` for persisting state across sessions.
- Utilize the `useStore` hook for accessing store state in components.
- Leverage the `immer` middleware for easier state updates with mutable syntax.
