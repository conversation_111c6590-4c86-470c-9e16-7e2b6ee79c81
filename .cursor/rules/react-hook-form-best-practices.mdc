---
description: Best practices for form handling with React Hook Form
globs: *.tsx,*.ts
alwaysApply: false
---

- Use the `useForm` hook for efficient form state management.
- Implement validation using Zod with `@hookform/resolvers` for type-safe form validation.
- Utilize the `Controller` component for integrating with custom inputs.
- Leverage the `useFormContext` hook for sharing form state across components.
