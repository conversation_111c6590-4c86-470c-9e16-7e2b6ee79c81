---
description: Best practices for React component development
globs: *.tsx,*.ts
alwaysApply: false
---

- Use functional components with hooks instead of class components for better performance and readability
- Implement proper state management using using libraries like Zustand for complex state scenarios
- Utilize React 19's new features like `use` hook for better data fetching and suspense integration
- Ensure proper prop type validation using TypeScript for enhanced type safety
- Utilize `useCallback` and `useMemo` for performance optimization
- Leverage `react-hook-form` for efficient form handling and validation
