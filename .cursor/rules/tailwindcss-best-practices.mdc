---
description: Best practices for styling with Tailwind CSS
globs: *.tsx,*.ts
alwaysApply: false
---

- Use utility-first approach for rapid development and maintainability
- Implement responsive design using Tailwind's built-in breakpoints
- Utilize the `@apply` directive for creating custom utility classes
- Leverage Tailwind's JIT mode for improved performance and smaller bundle sizes
- Utilize `tailwind-merge` for efficient class merging and overriding
- Leverage `tailwindcss-animate` for easy animation implementation
