---
description:
globs: *.tsx,*.ts
alwaysApply: false
---
# Development Workflow Guide

## Available Scripts
- `pnpm dev`: Start development server with content collections
- `pnpm build`: Build the application and content collections
- `pnpm start`: Start production server
- `pnpm lint`: Run Biome linter
- `pnpm format`: Format code with Biome
- `pnpm db:generate`: Generate new migration files based on schema changes
- `pnpm db:migrate`: Apply pending migrations to the database
- `pnpm db:push`: Sync schema changes directly to the database (development only)
- `pnpm db:studio`: Open Drizzle Studio for database inspection and management
- `pnpm email`: Start email template development server

## Development Process
1. Use TypeScript for all new code
2. Follow Biome formatting rules
3. Write server actions in `src/actions/`
4. Use Zustand for client-side state
5. Implement database changes through Drizzle migrations
6. Use Radix UI components for consistent UI
7. Follow the established directory structure
8. Write tests for new features
9. Update content collections when adding new content
10. Use environment variables from `env.example`

## Code Style
- Use functional components with hooks
- Implement proper error handling
- Follow TypeScript best practices
- Use proper type definitions
- Document complex logic
- Keep components small and focused
- Use proper naming conventions
