'use client';

import { <PERSON>Button } from '@/components/magicui/rainbow-button';
import { ShimmerButton } from '@/components/magicui/shimmer-button';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Download, Loader2, Mic, Play, RotateCcw, Upload } from 'lucide-react';
import { useCallback, useRef, useState } from 'react';
import { AudioRecorder } from 'react-audio-voice-recorder';

type Step = 'select' | 'input' | 'generating' | 'completed';
type Mode = 'record' | 'upload';

export default function HeroSection() {
  const [step, setStep] = useState<Step>('select');
  const [mode, setMode] = useState<Mode>('record');
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [textInput, setTextInput] = useState('');
  const [generatedAudio, setGeneratedAudio] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioPlayerRef = useRef<HTMLAudioElement>(null);

  const sampleText =
    "Hello everyone! I'm trying out this amazing AI voice cloning technology. It's truly incredible, and I'm excited to experience it with you!";

  // Handle audio recording completion
  const handleAudioStop = useCallback((audioBlob: Blob) => {
    setAudioBlob(audioBlob);
    setStep('input');
  }, []);

  // Handle file upload
  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        setAudioFile(file);
        setStep('input');
      }
    },
    []
  );

  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file?.type.startsWith('audio/')) {
      setAudioFile(file);
      setStep('input');
    }
  }, []);

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
    },
    []
  );

  // Generate speech from text and audio
  const handleGenerateSpeech = async () => {
    if (!textInput.trim()) {
      alert('Please enter some text to convert to speech');
      return;
    }

    setIsGenerating(true);
    setStep('generating');

    try {
      // First create voice clone
      const formData = new FormData();

      if (audioBlob) {
        // Convert blob to file for recording
        const audioFile = new File([audioBlob], 'recording.wav', {
          type: 'audio/wav',
        });
        formData.append('audio', audioFile);
      } else if (audioFile) {
        // Use uploaded file
        formData.append('audio', audioFile);
      } else {
        throw new Error('No audio data available');
      }

      formData.append('name', 'temp-voice-' + Date.now());
      formData.append('gender', 'male');
      formData.append('fullName', 'Anonymous User');
      formData.append('email', '<EMAIL>');
      formData.append('consent', 'true');

      const createResponse = await fetch('/api/voice-clone/create', {
        method: 'POST',
        body: formData,
      });

      const createResult = await createResponse.json();

      if (!createResult.success) {
        throw new Error(createResult.error || 'Failed to create voice clone');
      }

      // Then generate speech
      const generateResponse = await fetch('/api/voice-clone/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: textInput,
          voiceId: createResult.voiceId,
        }),
      });

      if (generateResponse.ok) {
        const audioBlob = await generateResponse.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        setGeneratedAudio(audioUrl);
        setStep('completed');
      } else {
        const error = await generateResponse.json();
        throw new Error(error.error || 'Failed to generate speech');
      }
    } catch (error) {
      console.error('Error:', error);
      alert(
        error instanceof Error ? error.message : 'Failed to generate speech'
      );
      setStep('input');
    } finally {
      setIsGenerating(false);
    }
  };

  // Reset to start over
  const handleReset = () => {
    setStep('select');
    setMode('record');
    setAudioBlob(null);
    setAudioFile(null);
    setTextInput('');
    setGeneratedAudio('');
    setIsGenerating(false);
  };

  return (
    <>
      <main
        id="hero"
        className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white overflow-hidden"
      >
        {/* Background Effects */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent" />

        <section className="relative">
          <div className="container mx-auto px-4 py-16">
            {/* Header */}
            <div className="text-center mb-16">
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                Clone Your Voice with AI
              </h1>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Transform your voice into AI-powered speech synthesis in just
                two simple steps
              </p>
            </div>

            {/* Main Interface */}
            <div className="max-w-4xl mx-auto">
              {step === 'select' && (
                <div className="space-y-8">
                  {/* Mode Switch */}
                  <div className="flex justify-center">
                    <div className="flex bg-gray-800/50 backdrop-blur-sm rounded-full p-2 border border-gray-700">
                      <ShimmerButton
                        onClick={() => setMode('record')}
                        className={`px-6 py-3 rounded-full transition-all ${
                          mode === 'record'
                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-500/25'
                            : 'bg-transparent text-gray-400 hover:text-white'
                        }`}
                        shimmerColor={mode === 'record' ? '#3b82f6' : '#6b7280'}
                      >
                        <Mic className="w-5 h-5 mr-2" />
                        Record
                      </ShimmerButton>
                      <ShimmerButton
                        onClick={() => setMode('upload')}
                        className={`px-6 py-3 rounded-full transition-all ${
                          mode === 'upload'
                            ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/25'
                            : 'bg-transparent text-gray-400 hover:text-white'
                        }`}
                        shimmerColor={mode === 'upload' ? '#8b5cf6' : '#6b7280'}
                      >
                        <Upload className="w-5 h-5 mr-2" />
                        Upload
                      </ShimmerButton>
                    </div>
                  </div>

                  {/* Dynamic Content Area */}
                  <div className="mt-12">
                    {mode === 'record' && (
                      <Card className="bg-gray-800/30 backdrop-blur-sm border-gray-700">
                        <CardContent className="p-12 text-center">
                          <div className="space-y-8">
                            <div className="relative">
                              <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-blue-500 to-purple-500 p-1">
                                <div className="w-full h-full rounded-full bg-gray-900 flex items-center justify-center">
                                  <AudioRecorder
                                    onRecordingComplete={handleAudioStop}
                                    audioTrackConstraints={{
                                      noiseSuppression: true,
                                      echoCancellation: true,
                                    }}
                                    downloadOnSavePress={false}
                                    downloadFileExtension="wav"
                                    showVisualizer={true}
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="space-y-4">
                              <h3 className="text-2xl font-semibold">
                                Start Recording
                              </h3>
                              <p className="text-gray-400 max-w-md mx-auto">
                                Click the microphone to start recording your
                                voice sample. Speak clearly for at least 10
                                seconds.
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {mode === 'upload' && (
                      <Card className="bg-gray-800/30 backdrop-blur-sm border-gray-700">
                        <CardContent className="p-12">
                          <div
                            className="border-2 border-dashed border-gray-600 rounded-xl p-12 text-center hover:border-purple-500 transition-colors cursor-pointer"
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <Upload className="w-16 h-16 mx-auto mb-6 text-gray-400" />
                            <h3 className="text-2xl font-semibold mb-4">
                              Upload Audio File
                            </h3>
                            <p className="text-gray-400 mb-6 max-w-md mx-auto">
                              Drag and drop your audio file here, or click to
                              browse. Supports MP3, WAV, M4A, and other audio
                              formats.
                            </p>
                            <Button
                              variant="outline"
                              className="border-gray-600 hover:border-purple-500"
                            >
                              Choose File
                            </Button>
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="audio/*"
                              onChange={handleFileUpload}
                              className="hidden"
                            />
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              )}

              {/* Text Input Step */}
              {step === 'input' && (
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold mb-4">Enter Your Text</h2>
                    <p className="text-gray-400">
                      Type the text you want to convert to speech using your
                      voice
                    </p>
                  </div>

                  <Card className="bg-gray-800/30 backdrop-blur-sm border-gray-700">
                    <CardContent className="p-8">
                      <div className="space-y-6">
                        <Textarea
                          value={textInput}
                          onChange={(e) => setTextInput(e.target.value)}
                          placeholder="Enter the text you want to convert to speech..."
                          rows={8}
                          className="text-lg bg-gray-900/50 border-gray-600 focus:border-blue-500 resize-none"
                        />

                        <div className="flex justify-between items-center">
                          <div className="text-sm text-gray-400">
                            {textInput.length} characters
                          </div>
                          <div className="flex gap-4">
                            <Button
                              variant="outline"
                              onClick={handleReset}
                              className="border-gray-600 hover:border-red-500 text-gray-300 hover:text-red-400"
                            >
                              <RotateCcw className="w-4 h-4 mr-2" />
                              Start Over
                            </Button>
                            <RainbowButton
                              onClick={handleGenerateSpeech}
                              disabled={!textInput.trim() || isGenerating}
                              className="px-8 py-3"
                            >
                              {isGenerating ? (
                                <>
                                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                                  Generating...
                                </>
                              ) : (
                                <>Generate Speech</>
                              )}
                            </RainbowButton>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Audio Preview */}
                  {(audioBlob || audioFile) && (
                    <Card className="bg-gray-800/30 backdrop-blur-sm border-gray-700">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                            <Mic className="w-6 h-6 text-green-400" />
                          </div>
                          <div>
                            <h4 className="font-semibold">
                              Voice Sample Ready
                            </h4>
                            <p className="text-sm text-gray-400">
                              {audioBlob ? 'Recorded audio' : audioFile?.name}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}

              {/* Generating Step */}
              {step === 'generating' && (
                <div className="text-center space-y-8">
                  <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-blue-500 to-purple-500 p-1 animate-spin">
                    <div className="w-full h-full rounded-full bg-gray-900 flex items-center justify-center">
                      <Loader2 className="w-16 h-16 text-white animate-spin" />
                    </div>
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold mb-4">
                      Generating Your Voice
                    </h2>
                    <p className="text-gray-400">
                      Please wait while we create your AI voice clone and
                      generate the speech...
                    </p>
                  </div>
                </div>
              )}

              {/* Completed Step */}
              {step === 'completed' && generatedAudio && (
                <div className="space-y-8">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold mb-4 text-green-400">
                      Voice Generated Successfully!
                    </h2>
                    <p className="text-gray-400">
                      Your AI voice clone has been created and your text has
                      been converted to speech
                    </p>
                  </div>

                  <Card className="bg-gradient-to-br from-green-900/20 to-blue-900/20 backdrop-blur-sm border-green-500/30">
                    <CardContent className="p-8">
                      <div className="space-y-6">
                        <div className="flex items-center gap-4 mb-6">
                          <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                            <Play className="w-6 h-6 text-green-400" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-green-400">
                              Generated Audio
                            </h4>
                            <p className="text-sm text-gray-400">
                              Ready to play and download
                            </p>
                          </div>
                        </div>

                        <audio
                          ref={audioPlayerRef}
                          controls
                          src={generatedAudio}
                          className="w-full h-12 rounded-xl bg-gray-800"
                          autoPlay
                        >
                          <track
                            kind="captions"
                            src=""
                            srcLang="en"
                            label="English captions"
                          />
                        </audio>

                        <div className="flex gap-4">
                          <Button
                            variant="outline"
                            onClick={() => {
                              const a = document.createElement('a');
                              a.href = generatedAudio;
                              a.download = 'generated-speech.mp3';
                              a.click();
                            }}
                            className="flex-1 border-green-500/30 hover:border-green-500 text-green-400 hover:text-green-300"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Download Audio
                          </Button>
                          <Button
                            onClick={handleReset}
                            className="flex-1 bg-blue-600 hover:bg-blue-700"
                          >
                            <RotateCcw className="w-4 h-4 mr-2" />
                            Create Another
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>

            {/* Sample Text Display */}
            {step === 'select' && (
              <div className="mt-16 text-center">
                <div className="max-w-2xl mx-auto p-6 bg-gray-800/20 backdrop-blur-sm rounded-xl border border-gray-700">
                  <h3 className="text-lg font-semibold mb-4 text-gray-300">
                    Sample Text to Read:
                  </h3>
                  <p className="text-gray-400 italic leading-relaxed">
                    "{sampleText}"
                  </p>
                </div>
              </div>
            )}
          </div>
        </section>
      </main>
    </>
  );
}
