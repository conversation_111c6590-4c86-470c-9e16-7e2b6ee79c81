@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* 
 * 1. Themes Documentation
 * https://mksaas.com/docs/themes

 * 2. Theme Generator
 * https://ui.shadcn.com/themes
 * https://tweakcn.com/
 * https://ui.pub/x/theme-gen
 *
 * default theme: Clean Slate
 * https://tweakcn.com/editor/theme
 * 
 * NOTICE: when you change the theme, you need to check the fonts and keep the animation variables
 *
 * 3. How to Add a Theme Selector to Your Next.js App
 * https://ouassim.tech/notes/how-to-add-a-theme-selector-to-your-nextjs-app/
 */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  /* fonts */
  --font-sans: var(--font-noto-sans);
  --font-mono: var(--font-noto-sans-mono);
  --font-serif: var(--font-noto-serif);
  --font-bricolage-grotesque: var(--font-bricolage-grotesque);

  /* animate */
  --animate-shiny-text: shiny-text 8s infinite;
  --animate-rainbow: rainbow var(--speed, 2s) infinite linear;
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  --animate-ripple: ripple var(--duration, 2s) ease calc(var(--i, 0) * .2s)
    infinite;
  --animate-pulse: pulse var(--duration) ease-out infinite;
  --animate-meteor: meteor 5s linear infinite;
  --animate-gradient: gradient 8s linear infinite;

  @keyframes shiny-text {
    0%,
    90%,
    100% {
      background-position: calc(-100% - var(--shiny-width)) 0;
    }
    30%,
    60% {
      background-position: calc(100% + var(--shiny-width)) 0;
    }
  }

  @keyframes rainbow {
    0% {
      background-position: 0%;
    }
    100% {
      background-position: 200%;
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }

  @keyframes ripple {
    0%,
    100% {
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      transform: translate(-50%, -50%) scale(0.9);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      box-shadow: 0 0 0 0 var(--pulse-color);
    }
    50% {
      box-shadow: 0 0 0 8px var(--pulse-color);
    }
  }

  @keyframes meteor {
    0% {
      transform: rotate(var(--angle)) translateX(0);
      opacity: 1;
    }
    70% {
      opacity: 1;
    }
    100% {
      transform: rotate(var(--angle)) translateX(-500px);
      opacity: 0;
    }
  }

  @keyframes gradient {
    to {
      background-position: var(--bg-size, 300%) 0;
    }
  }
}

:root {
  --background: oklch(0.98 0.0 247.86);
  --foreground: oklch(0.28 0.04 260.03);
  --card: oklch(1.0 0 0);
  --card-foreground: oklch(0.28 0.04 260.03);
  --popover: oklch(1.0 0 0);
  --popover-foreground: oklch(0.28 0.04 260.03);
  --primary: oklch(0.59 0.2 277.12);
  --primary-foreground: oklch(1.0 0 0);
  --secondary: oklch(0.93 0.01 264.53);
  --secondary-foreground: oklch(0.37 0.03 259.73);
  --muted: oklch(0.97 0.0 264.54);
  --muted-foreground: oklch(0.55 0.02 264.36);
  --accent: oklch(0.93 0.03 272.79);
  --accent-foreground: oklch(0.37 0.03 259.73);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(1.0 0 0);
  --border: oklch(0.87 0.01 258.34);
  --input: oklch(0.87 0.01 258.34);
  --ring: oklch(0.59 0.2 277.12);
  --chart-1: oklch(0.59 0.2 277.12);
  --chart-2: oklch(0.51 0.23 276.97);
  --chart-3: oklch(0.46 0.21 277.02);
  --chart-4: oklch(0.4 0.18 277.37);
  --chart-5: oklch(0.36 0.14 278.7);
  --sidebar: oklch(0.97 0.0 264.54);
  --sidebar-foreground: oklch(0.28 0.04 260.03);
  --sidebar-primary: oklch(0.59 0.2 277.12);
  --sidebar-primary-foreground: oklch(1.0 0 0);
  --sidebar-accent: oklch(0.93 0.03 272.79);
  --sidebar-accent-foreground: oklch(0.37 0.03 259.73);
  --sidebar-border: oklch(0.87 0.01 258.34);
  --sidebar-ring: oklch(0.59 0.2 277.12);
  --font-sans: var(--font-noto-sans);
  --font-serif: var(--font-noto-serif);
  --font-mono: var(--font-noto-sans-mono);
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px
    hsl(0 0% 0% / 0.1);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 2px 4px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 4px 6px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 8px 10px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: oklch(0.21 0.04 265.75);
  --foreground: oklch(0.93 0.01 255.51);
  --card: oklch(0.28 0.04 260.03);
  --card-foreground: oklch(0.93 0.01 255.51);
  --popover: oklch(0.28 0.04 260.03);
  --popover-foreground: oklch(0.93 0.01 255.51);
  --primary: oklch(0.68 0.16 276.93);
  --primary-foreground: oklch(0.21 0.04 265.75);
  --secondary: oklch(0.34 0.03 260.91);
  --secondary-foreground: oklch(0.87 0.01 258.34);
  --muted: oklch(0.28 0.04 260.03);
  --muted-foreground: oklch(0.71 0.02 261.32);
  --accent: oklch(0.37 0.03 259.73);
  --accent-foreground: oklch(0.87 0.01 258.34);
  --destructive: oklch(0.64 0.21 25.33);
  --destructive-foreground: oklch(0.21 0.04 265.75);
  --border: oklch(0.45 0.03 256.8);
  --input: oklch(0.45 0.03 256.8);
  --ring: oklch(0.68 0.16 276.93);
  --chart-1: oklch(0.68 0.16 276.93);
  --chart-2: oklch(0.59 0.2 277.12);
  --chart-3: oklch(0.51 0.23 276.97);
  --chart-4: oklch(0.46 0.21 277.02);
  --chart-5: oklch(0.4 0.18 277.37);
  --sidebar: oklch(0.28 0.04 260.03);
  --sidebar-foreground: oklch(0.93 0.01 255.51);
  --sidebar-primary: oklch(0.68 0.16 276.93);
  --sidebar-primary-foreground: oklch(0.21 0.04 265.75);
  --sidebar-accent: oklch(0.37 0.03 259.73);
  --sidebar-accent-foreground: oklch(0.87 0.01 258.34);
  --sidebar-border: oklch(0.45 0.03 256.8);
  --sidebar-ring: oklch(0.68 0.16 276.93);
  --font-sans: var(--font-noto-sans);
  --font-serif: var(--font-noto-serif);
  --font-mono: var(--font-noto-sans-mono);
  --radius: 0.5rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px
    hsl(0 0% 0% / 0.1);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 1px 2px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 2px 4px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 4px 6px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.1), 0px 8px 10px -2px
    hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

body {
  @apply overscroll-none bg-transparent;
}

.text-gradient_indigo-purple {
  background: linear-gradient(90deg, #6366f1 0%, rgb(168 85 247 / 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/*
 * 1. all the themes are copied from the shadcn-ui dashboard example
 * https://github.com/shadcn-ui/ui/blob/main/apps/v4/app/(examples)/dashboard/theme.css
 * https://github.com/TheOrcDev/orcish-dashboard/blob/main/app/globals.css
 *
 * 2. we suggest always using the default theme for better user experience,
 * and then override the .theme-default to customize the theme
 */
.theme-default {
  /* default theme */
}

.theme-neutral {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }
}

.theme-blue {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);

  @variant dark {
    --primary: var(--color-blue-500);
    --primary-foreground: var(--color-blue-50);
  }
}

.theme-green {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);

  @variant dark {
    --primary: var(--color-lime-600);
    --primary-foreground: var(--color-lime-50);
  }
}

.theme-amber {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);

  @variant dark {
    --primary: var(--color-amber-500);
    --primary-foreground: var(--color-amber-50);
  }
}

/**
 * Hide scrollbar for layout shift when using the theme selector
 */
/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
